@extends('layouts.supplier')

@section('title', 'Manajemen Retur - Dashboard Supplier')
@section('page-title', 'Manajemen Retur')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl font-bold text-gray-900">Retur dari Gudang</h1>
                    <p class="text-gray-600 mt-1">Tinjau dan kelola retur produk dari gudang pusat</p>
                </div>
                <div class="supplier-dashboard-info-card">
                    <div class="flex items-center space-x-2">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <div class="text-sm">
                            <div class="font-medium text-gray-900">Pilihan Aksi:</div>
                            <div class="text-gray-600">Hapus Permanen atau Kirim Ulang</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['total_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Total Retur</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['requested_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Retur Diminta</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['approved_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Retur Disetujui</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon purple">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['completed_returns']) }}</div>
            <div class="supplier-dashboard-stat-label">Retur Selesai</div>
        </div>

        <div class="supplier-dashboard-stat-card">
            <div class="supplier-dashboard-stat-icon red">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="supplier-dashboard-stat-value">{{ number_format($stats['cancelled_deliveries']) }}</div>
            <div class="supplier-dashboard-stat-label">Pengiriman Dibatalkan</div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <!-- Month Filter -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Periode</label>
                    <input type="month"
                           name="month"
                           value="{{ $filterMonth }}"
                           class="supplier-dashboard-input">
                </div>

                <!-- Search -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text"
                           name="search"
                           value="{{ request('search') }}"
                           placeholder="Cari produk..."
                           class="supplier-dashboard-input">
                </div>

                <!-- Filter Button -->
                <div class="flex items-end">
                    <button type="submit" class="w-full supplier-dashboard-btn supplier-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Returns Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Retur dari Gudang</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <!-- <th class="px-6 py-3">Produk</th> -->
                            <th class="px-6 py-3">Sumber</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($returns as $return)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <!-- <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->product->name ?? 'N/A' }}</div>
                                <div class="text-xs text-gray-500 mt-1">{{ $return->product->category ?? 'Tanpa Kategori' }}</div>
                            </td> -->
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->source }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($return->quantity) }}</div>
                                <div class="text-xs text-gray-500">unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">{{ $return->reason_in_indonesian }}</div>
                                @if($return->description)
                                <div class="text-xs text-gray-500 mt-1">{{ Str::limit($return->description, 50) }}</div>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $return->return_date ? $return->return_date->format('d M Y') : 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="supplier-dashboard-returns-actions">
                                    <!-- Accept & Delete Forever Button -->
                                    <button onclick="openDeleteForeverModal('{{ $return->id }}', '{{ $return->product->name ?? 'N/A' }}')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Hapus Permanen
                                    </button>

                                    <!-- Accept & Send Again Button -->
                                    <button onclick="openSendAgainModal('{{ $return->id }}', '{{ $return->product->name ?? 'N/A' }}', '{{ $return->quantity }}')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                                        <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Kirim Ulang
                                    </button>

                                    <!-- View Details Link -->
                                    <a href="{{ route('supplier.returns.show', $return) }}"
                                       class="text-xs text-blue-600 hover:text-blue-800 text-center font-medium">
                                        Lihat Detail
                                    </a>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada permintaan retur</p>
                                    <p>Permintaan retur dari toko akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Cancelled Deliveries Table -->
    <div class="supplier-dashboard-card">
        <div class="supplier-dashboard-card-header">
            <h2 class="supplier-dashboard-card-title">Pengiriman yang Dibatalkan</h2>
        </div>
        <div class="supplier-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Harga Satuan</th>
                            <th class="px-6 py-3">Total Harga</th>
                            <th class="px-6 py-3">Tanggal Pengiriman</th>
                            <th class="px-6 py-3">Catatan</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($cancelledDeliveries as $delivery)
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->product->name ?? 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ number_format($delivery->quantity) }} unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp {{ number_format($delivery->unit_price, 0, ',', '.') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">Rp {{ number_format($delivery->total_price, 0, ',', '.') }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900">{{ $delivery->delivery_date ? $delivery->delivery_date->format('d M Y') : 'N/A' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600">{{ $delivery->notes ?? 'Tidak ada catatan' }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <button onclick="openDeleteCancelledModal('{{ $delivery->id }}', '{{ $delivery->product->name ?? 'N/A' }}')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-danger text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                        </svg>
                                        Hapus
                                    </button>
                                    <button onclick="openResendModal('{{ $delivery->id }}', '{{ $delivery->product->name ?? 'N/A' }}', '{{ $delivery->quantity }}')"
                                            class="supplier-dashboard-btn supplier-dashboard-btn-primary text-xs">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                        </svg>
                                        Kirim Ulang
                                    </button>
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum ada pengiriman yang dibatalkan</p>
                                    <p>Pengiriman yang dibatalkan akan muncul di sini</p>
                                </div>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

        </div>
    </div>
</div>

<!-- Delete Forever Modal -->
<div id="deleteForeverModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Hapus Produk Permanen</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeDeleteForeverModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="deleteForeverForm" method="POST">
            @csrf
            @method('DELETE')
            <div class="supplier-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penghapusan Permanen</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Anda akan menghapus permanen produk <strong id="deleteForeverProductName"></strong> dari sistem.
                        </p>
                        <p class="text-sm text-red-600 font-medium">
                            ⚠️ Produk ini tidak akan dikirim ulang dan akan dihapus dari inventori secara permanen.
                        </p>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeDeleteForeverModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-danger">
                    Ya, Hapus Permanen
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Send Again Modal -->
<div id="sendAgainModal" class="supplier-dashboard-modal">
    <div class="supplier-dashboard-modal-content">
        <div class="supplier-dashboard-modal-header">
            <h3 class="supplier-dashboard-modal-title">Kirim Produk Ulang</h3>
            <button type="button" class="supplier-dashboard-modal-close" onclick="closeSendAgainModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="sendAgainForm" method="POST">
            @csrf
            <div class="supplier-dashboard-modal-body">
                <div class="space-y-4">
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0">
                            <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                                <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="flex-1">
                            <h4 class="text-lg font-medium text-gray-900 mb-2">Kirim Produk Pengganti</h4>
                            <p class="text-sm text-gray-600 mb-2">
                                Anda akan mengirim ulang produk <strong id="sendAgainProductName"></strong>
                            </p>
                            <p class="text-sm text-gray-600 mb-4">
                                Jumlah: <strong id="sendAgainQuantity"></strong> unit
                            </p>
                            <p class="text-sm text-blue-600 font-medium">
                                ✓ Produk pengganti akan dikirim ke gudang untuk menggantikan produk yang bermasalah.
                            </p>
                        </div>
                    </div>

                    <div>
                        <label for="send_again_delivery_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Pengiriman <span class="text-red-500">*</span>
                        </label>
                        <input type="date"
                               id="send_again_delivery_date"
                               name="delivery_date"
                               min="{{ date('Y-m-d') }}"
                               class="supplier-dashboard-input"
                               required>
                    </div>

                    <div>
                        <label for="send_again_notes" class="block text-sm font-medium text-gray-700 mb-2">
                            Catatan (Opsional)
                        </label>
                        <textarea id="send_again_notes"
                                  name="notes"
                                  rows="3"
                                  class="supplier-dashboard-textarea"
                                  placeholder="Catatan untuk pengiriman pengganti..."></textarea>
                    </div>
                </div>
            </div>
            <div class="supplier-dashboard-modal-footer">
                <button type="button" onclick="closeSendAgainModal()" class="supplier-dashboard-btn supplier-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="supplier-dashboard-btn supplier-dashboard-btn-primary">
                    Kirim Produk Pengganti
                </button>
            </div>
        </form>
    </div>
</div>



@push('scripts')
<script>
// Delete Forever Modal Functions
function openDeleteForeverModal(returnId, productName) {
    const modal = document.getElementById('deleteForeverModal');
    const form = document.getElementById('deleteForeverForm');
    const productNameElement = document.getElementById('deleteForeverProductName');

    form.action = `/supplier/returns/${returnId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

function closeDeleteForeverModal() {
    const modal = document.getElementById('deleteForeverModal');
    modal.classList.remove('active');
}

// Send Again Modal Functions
function openSendAgainModal(returnId, productName, quantity) {
    const modal = document.getElementById('sendAgainModal');
    const form = document.getElementById('sendAgainForm');
    const productNameElement = document.getElementById('sendAgainProductName');
    const quantityElement = document.getElementById('sendAgainQuantity');

    form.action = `/supplier/returns/${returnId}/resend`;
    productNameElement.textContent = productName;
    quantityElement.textContent = quantity;

    modal.classList.add('active');
}

function closeSendAgainModal() {
    const modal = document.getElementById('sendAgainModal');
    modal.classList.remove('active');
    document.getElementById('sendAgainForm').reset();
}

// Cancelled Delivery Functions (for the cancelled deliveries section)
function openResendModal(deliveryId, productName, quantity) {
    const modal = document.getElementById('sendAgainModal');
    const form = document.getElementById('sendAgainForm');
    const productNameElement = document.getElementById('sendAgainProductName');
    const quantityElement = document.getElementById('sendAgainQuantity');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}/resend`;
    productNameElement.textContent = productName;
    quantityElement.textContent = quantity;

    modal.classList.add('active');
}

function openDeleteCancelledModal(deliveryId, productName) {
    const modal = document.getElementById('deleteForeverModal');
    const form = document.getElementById('deleteForeverForm');
    const productNameElement = document.getElementById('deleteForeverProductName');

    form.action = `/supplier/cancelled-deliveries/${deliveryId}`;
    productNameElement.textContent = productName;

    modal.classList.add('active');
}

// Close modals when clicking outside
document.getElementById('deleteForeverModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteForeverModal();
    }
});

document.getElementById('sendAgainModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeSendAgainModal();
    }
});
</script>
@endpush
@endsection
