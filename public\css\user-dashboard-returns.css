/* Indah Berkah Abadi - User Dashboard Returns CSS */
/* Independent CSS for user dashboard returns with clean, professional design */

/*
 * Z-INDEX HIERARCHY:
 * - Modal overlays: 500-999 (high priority)
 * - Dropdown elements: 100-499 (medium-high priority)
 * - Interactive elements: 10-99 (low-medium priority)
 * - Hover states and tooltips: 2-9 (low priority)
 * - Main content: 1 (lowest priority)
 */

/* ===== LAYOUT COMPONENTS ===== */

.user-dashboard-returns-container {
    padding: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

/* ===== CARD COMPONENTS ===== */

.user-dashboard-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.user-dashboard-card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-wrap: wrap;
    gap: 1rem;
}

.user-dashboard-card-header-content {
    flex: 1;
    min-width: 0;
}

.user-dashboard-card-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    line-height: 1.3;
}

.user-dashboard-card-subtitle {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0.25rem 0 0 0;
    line-height: 1.4;
}

.user-dashboard-card-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    flex-wrap: wrap;
}

.user-dashboard-card-content {
    padding: 1.5rem;
}

/* ===== BUTTON COMPONENTS ===== */

.user-dashboard-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.625rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 8px;
    border: 1px solid transparent;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    line-height: 1.4;
    min-height: 44px; /* Mobile touch target */
}

.user-dashboard-btn-primary {
    background-color: #3b82f6;
    color: white;
    border-color: #3b82f6;
}

.user-dashboard-btn-primary:hover {
    background-color: #2563eb;
    border-color: #2563eb;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

.user-dashboard-btn-secondary {
    background-color: #6b7280;
    color: white;
    border-color: #6b7280;
}

.user-dashboard-btn-secondary:hover {
    background-color: #4b5563;
    border-color: #4b5563;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(107, 114, 128, 0.3);
}

.user-dashboard-btn-sm {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
    min-height: 36px;
}

.user-dashboard-btn-full {
    width: 100%;
}

/* ===== STATISTICS COMPONENTS ===== */

.user-dashboard-stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.user-dashboard-stat-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease-in-out;
}

.user-dashboard-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-dashboard-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.user-dashboard-stat-icon-blue {
    background-color: #dbeafe;
    color: #3b82f6;
}

.user-dashboard-stat-icon-yellow {
    background-color: #fef3c7;
    color: #f59e0b;
}

.user-dashboard-stat-icon-green {
    background-color: #d1fae5;
    color: #10b981;
}

.user-dashboard-stat-content {
    flex: 1;
    min-width: 0;
}

.user-dashboard-stat-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin: 0 0 0.25rem 0;
}

.user-dashboard-stat-value {
    font-size: 1.875rem;
    font-weight: 700;
    color: #111827;
    margin: 0;
    line-height: 1.2;
}

/* ===== FORM COMPONENTS ===== */

.user-dashboard-filter-form {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.user-dashboard-form-group {
    display: flex;
    flex-direction: column;
}

.user-dashboard-form-group-button {
    align-self: end;
}

.user-dashboard-form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.user-dashboard-form-input,
.user-dashboard-form-select {
    padding: 0.625rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    font-size: 0.875rem;
    background-color: white;
    transition: all 0.2s ease-in-out;
    min-height: 44px; /* Mobile touch target */
}

.user-dashboard-form-input:focus,
.user-dashboard-form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* ===== STATUS BADGES ===== */

.user-dashboard-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    text-transform: capitalize;
}

.user-dashboard-status-requested {
    background-color: #fef3c7;
    color: #92400e;
}

.user-dashboard-status-approved {
    background-color: #dbeafe;
    color: #1e40af;
}

.user-dashboard-status-in_transit {
    background-color: #e0e7ff;
    color: #5b21b6;
}

.user-dashboard-status-completed {
    background-color: #d1fae5;
    color: #065f46;
}

.user-dashboard-status-rejected {
    background-color: #fee2e2;
    color: #991b1b;
}

/* ===== EMPTY STATE ===== */

.user-dashboard-empty-state {
    text-align: center;
    padding: 2rem;
}

.user-dashboard-empty-icon {
    width: 4rem;
    height: 4rem;
    margin: 0 auto 1rem auto;
    color: #d1d5db;
}

.user-dashboard-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #374151;
    margin: 0 0 0.5rem 0;
}

.user-dashboard-empty-description {
    font-size: 0.875rem;
    color: #6b7280;
    margin: 0 0 1.5rem 0;
}

/* ===== PAGINATION ===== */

.user-dashboard-pagination {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 768px) {
    .user-dashboard-returns-container {
        padding: 1rem;
        gap: 1rem;
    }
    
    .user-dashboard-card-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .user-dashboard-card-actions {
        justify-content: stretch;
    }
    
    .user-dashboard-btn {
        flex: 1;
        justify-content: center;
    }
    
    .user-dashboard-stats-grid {
        grid-template-columns: 1fr;
    }
    
    .user-dashboard-filter-form {
        grid-template-columns: 1fr;
    }
    
    .user-dashboard-stat-card {
        padding: 1rem;
    }
    
    .user-dashboard-stat-icon {
        width: 40px;
        height: 40px;
    }
    
    .user-dashboard-stat-value {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .user-dashboard-returns-container {
        padding: 0.75rem;
    }
    
    .user-dashboard-card-content,
    .user-dashboard-card-header {
        padding: 1rem;
    }
    
    .user-dashboard-card-title {
        font-size: 1.25rem;
    }
    
    .user-dashboard-stat-card {
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
    }
    
    .user-dashboard-stat-content {
        text-align: center;
    }
}
