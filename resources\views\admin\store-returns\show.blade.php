@extends('layouts.admin')

@section('title', 'Detail Retur dari <PERSON> - Admin Dashboard')

@section('content')
<div class="admin-dashboard-main-content space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Detail Retur dari Toko</h1>
                    <p class="text-gray-600 mt-1">Informasi lengkap retur dari {{ $storeReturn->store->name }}</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('admin.store-returns.index') }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Return Details -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Basic Information -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Informasi Retur</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">ID Retur</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-mono">{{ $storeReturn->id }}</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Toko</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <div class="font-medium">{{ $storeReturn->store->name }}</div>
                            <div class="text-gray-500">{{ $storeReturn->store->location }}</div>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Produk</dt>
                        <dd class="mt-1 text-sm text-gray-900">
                            <div class="font-medium">{{ $storeReturn->product->name }}</div>
                            @if($storeReturn->product->category)
                            <div class="text-gray-500">{{ $storeReturn->product->category }}</div>
                            @endif
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Jumlah</dt>
                        <dd class="mt-1 text-sm text-gray-900 font-medium">{{ number_format($storeReturn->quantity) }} unit</dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Alasan</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->reason_in_indonesian }}</dd>
                    </div>
                    @if($storeReturn->description)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Deskripsi</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->description }}</dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>

        <!-- Status and Dates -->
        <div class="admin-dashboard-card">
            <div class="admin-dashboard-card-header">
                <h2 class="admin-dashboard-card-title">Status & Tanggal</h2>
            </div>
            <div class="admin-dashboard-card-content">
                <dl class="space-y-4">
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Status</dt>
                        <dd class="mt-1">
                            <span class="px-3 py-1 text-sm font-medium rounded-full 
                                @if($storeReturn->status === 'requested') bg-yellow-100 text-yellow-800
                                @elseif($storeReturn->status === 'approved') bg-blue-100 text-blue-800
                                @elseif($storeReturn->status === 'in_transit') bg-purple-100 text-purple-800
                                @elseif($storeReturn->status === 'completed') bg-green-100 text-green-800
                                @elseif($storeReturn->status === 'rejected') bg-red-100 text-red-800
                                @else bg-gray-100 text-gray-800
                                @endif">
                                {{ $storeReturn->status_in_indonesian }}
                            </span>
                        </dd>
                    </div>
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tanggal Retur</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->return_date->format('d M Y, H:i') }}</dd>
                    </div>
                    @if($storeReturn->approved_date)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tanggal Disetujui</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->approved_date->format('d M Y, H:i') }}</dd>
                    </div>
                    @endif
                    @if($storeReturn->completed_date)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Tanggal Selesai</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->completed_date->format('d M Y, H:i') }}</dd>
                    </div>
                    @endif
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Diminta oleh</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->requestedBy->name }}</dd>
                    </div>
                    @if($storeReturn->approvedBy)
                    <div>
                        <dt class="text-sm font-medium text-gray-500">Disetujui oleh</dt>
                        <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->approvedBy->name }}</dd>
                    </div>
                    @endif
                </dl>
            </div>
        </div>
    </div>

    <!-- Supplier Information (if forwarded) -->
    @if($storeReturn->supplier)
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Supplier</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <dl class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <dt class="text-sm font-medium text-gray-500">Nama Supplier</dt>
                    <dd class="mt-1 text-sm text-gray-900 font-medium">{{ $storeReturn->supplier->name }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Kontak</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->supplier->contact_person }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Telepon</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->supplier->phone }}</dd>
                </div>
                <div>
                    <dt class="text-sm font-medium text-gray-500">Email</dt>
                    <dd class="mt-1 text-sm text-gray-900">{{ $storeReturn->supplier->email }}</dd>
                </div>
            </dl>
        </div>
    </div>
    @endif

    <!-- Admin Notes -->
    @if($storeReturn->admin_notes)
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Catatan Admin</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="text-sm text-gray-900 whitespace-pre-line">{{ $storeReturn->admin_notes }}</div>
        </div>
    </div>
    @endif

    <!-- Actions -->
    @if($storeReturn->status === 'requested')
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Aksi</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row gap-3">
                <button onclick="openApproveModal('{{ $storeReturn->id }}')" 
                        class="admin-dashboard-btn admin-dashboard-btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Setujui Retur
                </button>
                <button onclick="openRejectModal('{{ $storeReturn->id }}')" 
                        class="admin-dashboard-btn admin-dashboard-btn-danger">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    Tolak Retur
                </button>
            </div>
        </div>
    </div>
    @elseif($storeReturn->status === 'approved')
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Aksi</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row gap-3">
                @if(!$storeReturn->supplier_id)
                <button onclick="openForwardToSupplierModal('{{ $storeReturn->id }}', '{{ $storeReturn->product->name }}')" 
                        class="admin-dashboard-btn admin-dashboard-btn-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    Teruskan ke Supplier
                </button>
                @endif
                <button onclick="openCompleteModal('{{ $storeReturn->id }}')" 
                        class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                    </svg>
                    Selesaikan Retur
                </button>
            </div>
        </div>
    </div>
    @endif
</div>

<!-- Include the same modals from index page -->
<!-- Note: In a real application, you might want to extract these modals to a shared partial -->

@endsection
