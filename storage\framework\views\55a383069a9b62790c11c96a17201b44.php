<?php $__env->startSection('title', 'Retur dari <PERSON> - Admin Dashboard'); ?>

<?php $__env->startSection('content'); ?>
<div class="admin-dashboard-main-content space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Retur dari <PERSON></h1>
                    <p class="text-gray-600 mt-1">Kelola permintaan retur produk dari toko-toko</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['total'] ?? 0)); ?></p>
                <p class="admin-dashboard-stat-label">Total Retur</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-yellow">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['requested'] ?? 0)); ?></p>
                <p class="admin-dashboard-stat-label">Menunggu</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-blue">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['approved'] ?? 0)); ?></p>
                <p class="admin-dashboard-stat-label">Disetujui</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-purple">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['forwarded_to_supplier'] ?? 0)); ?></p>
                <p class="admin-dashboard-stat-label">Diteruskan</p>
            </div>
        </div>

        <div class="admin-dashboard-stat-card">
            <div class="admin-dashboard-stat-icon admin-dashboard-stat-icon-green">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <div class="admin-dashboard-stat-content">
                <p class="admin-dashboard-stat-value"><?php echo e(number_format($stats['completed'] ?? 0)); ?></p>
                <p class="admin-dashboard-stat-label">Selesai</p>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <form method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                <div>
                    <label for="search" class="block text-sm font-medium text-gray-700 mb-1">Cari</label>
                    <input type="text" id="search" name="search" value="<?php echo e(request('search')); ?>" 
                           placeholder="Cari produk atau toko..." class="admin-dashboard-input">
                </div>
                <div>
                    <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
                    <select id="status" name="status" class="admin-dashboard-select">
                        <option value="">Semua Status</option>
                        <option value="requested" <?php echo e(request('status') === 'requested' ? 'selected' : ''); ?>>Diminta</option>
                        <option value="approved" <?php echo e(request('status') === 'approved' ? 'selected' : ''); ?>>Disetujui</option>
                        <option value="in_transit" <?php echo e(request('status') === 'in_transit' ? 'selected' : ''); ?>>Diteruskan</option>
                        <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Selesai</option>
                        <option value="rejected" <?php echo e(request('status') === 'rejected' ? 'selected' : ''); ?>>Ditolak</option>
                    </select>
                </div>
                <div>
                    <label for="store_id" class="block text-sm font-medium text-gray-700 mb-1">Toko</label>
                    <select id="store_id" name="store_id" class="admin-dashboard-select">
                        <option value="">Semua Toko</option>
                        <?php $__currentLoopData = $stores; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $store): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($store->id); ?>" <?php echo e(request('store_id') == $store->id ? 'selected' : ''); ?>>
                                <?php echo e($store->name); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div>
                    <label for="month" class="block text-sm font-medium text-gray-700 mb-1">Bulan</label>
                    <input type="month" id="month" name="month" value="<?php echo e($filterMonth); ?>" class="admin-dashboard-input">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary w-full">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"></path>
                        </svg>
                        Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Store Returns Table -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Daftar Retur dari Toko</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <div class="overflow-x-auto">
                <table class="w-full text-sm text-left">
                    <thead class="text-xs text-gray-700 uppercase bg-gray-50">
                        <tr>
                            <th class="px-6 py-3">Toko</th>
                            <th class="px-6 py-3">Produk</th>
                            <th class="px-6 py-3">Jumlah</th>
                            <th class="px-6 py-3">Alasan</th>
                            <th class="px-6 py-3">Tanggal</th>
                            <th class="px-6 py-3">Status</th>
                            <th class="px-6 py-3">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $returns; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $return): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="bg-white border-b hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->store->name); ?></div>
                                <div class="text-sm text-gray-500"><?php echo e($return->store->location); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->product->name); ?></div>
                                <?php if($return->supplier): ?>
                                <div class="text-sm text-gray-500">Diteruskan ke: <?php echo e($return->supplier->name); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e(number_format($return->quantity)); ?></div>
                                <div class="text-xs text-gray-500">unit</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-600"><?php echo e($return->reason_in_indonesian); ?></div>
                                <?php if($return->description): ?>
                                <div class="text-xs text-gray-500 mt-1"><?php echo e(Str::limit($return->description, 50)); ?></div>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4">
                                <div class="font-medium text-gray-900"><?php echo e($return->return_date->format('d M Y')); ?></div>
                                <div class="text-xs text-gray-500"><?php echo e($return->return_date->format('H:i')); ?></div>
                            </td>
                            <td class="px-6 py-4">
                                <span class="px-2 py-1 text-xs font-medium rounded-full 
                                    <?php if($return->status === 'requested'): ?> bg-yellow-100 text-yellow-800
                                    <?php elseif($return->status === 'approved'): ?> bg-blue-100 text-blue-800
                                    <?php elseif($return->status === 'in_transit'): ?> bg-purple-100 text-purple-800
                                    <?php elseif($return->status === 'completed'): ?> bg-green-100 text-green-800
                                    <?php elseif($return->status === 'rejected'): ?> bg-red-100 text-red-800
                                    <?php else: ?> bg-gray-100 text-gray-800
                                    <?php endif; ?>">
                                    <?php if($return->status === 'requested'): ?> Diminta
                                    <?php elseif($return->status === 'approved'): ?> Disetujui
                                    <?php elseif($return->status === 'in_transit'): ?> Diteruskan
                                    <?php elseif($return->status === 'completed'): ?> Selesai
                                    <?php elseif($return->status === 'rejected'): ?> Ditolak
                                    <?php else: ?> <?php echo e(ucfirst($return->status ?? '')); ?>

                                    <?php endif; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center space-x-2">
                                    <a href="<?php echo e(route('admin.store-returns.show', $return)); ?>" 
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Lihat
                                    </a>
                                    <?php if($return->status === 'requested'): ?>
                                    <button onclick="openApproveModal('<?php echo e($return->id); ?>')" 
                                            class="text-green-600 hover:text-green-800 text-sm font-medium">
                                        Setujui
                                    </button>
                                    <button onclick="openRejectModal('<?php echo e($return->id); ?>')" 
                                            class="text-red-600 hover:text-red-800 text-sm font-medium">
                                        Tolak
                                    </button>
                                    <?php elseif($return->status === 'approved'): ?>
                                    <?php if(!$return->supplier_id): ?>
                                    <button onclick="openForwardToSupplierModal('<?php echo e($return->id); ?>', '<?php echo e($return->product->name); ?>')" 
                                            class="text-purple-600 hover:text-purple-800 text-sm font-medium">
                                        Teruskan ke Supplier
                                    </button>
                                    <?php endif; ?>
                                    <button onclick="openCompleteModal('<?php echo e($return->id); ?>')" 
                                            class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                        Selesaikan
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="7" class="px-6 py-12 text-center">
                                <div class="text-gray-500">
                                    <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"></path>
                                    </svg>
                                    <p class="text-lg font-medium mb-2">Belum Ada Retur dari Toko</p>
                                    <p class="mb-4">Belum ada retur yang dikirim dari toko-toko</p>
                                </div>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <?php if(isset($returns) && $returns->hasPages()): ?>
            <div class="mt-6">
                <?php echo e($returns->links()); ?>

            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Approve Modal -->
<div id="approveModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Setujui Retur dari Toko</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeApproveModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="approveForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Persetujuan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyetujui retur dari toko ini? Setelah disetujui, Anda dapat memilih untuk meneruskannya ke supplier atau menyelesaikannya langsung.
                        </p>
                        <div>
                            <label for="approve_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan (Opsional)
                            </label>
                            <textarea id="approve_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Tambahkan catatan untuk persetujuan..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeApproveModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Ya, Setujui Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Reject Modal -->
<div id="rejectModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Tolak Retur dari Toko</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeRejectModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="rejectForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penolakan</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menolak retur dari toko ini? Berikan alasan penolakan yang jelas.
                        </p>
                        <div>
                            <label for="reject_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Alasan Penolakan <span class="text-red-500">*</span>
                            </label>
                            <textarea id="reject_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Jelaskan alasan penolakan retur..." required></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeRejectModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-danger">
                    Ya, Tolak Retur
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Forward to Supplier Modal -->
<div id="forwardToSupplierModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Teruskan ke Supplier</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeForwardToSupplierModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="forwardToSupplierForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Teruskan Retur ke Supplier</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Pilih supplier untuk meneruskan retur produk <strong id="forwardProductName"></strong> dari toko.
                        </p>
                        <div class="space-y-4">
                            <div>
                                <label for="forward_supplier_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    Supplier <span class="text-red-500">*</span>
                                </label>
                                <select id="forward_supplier_id" name="supplier_id" class="admin-dashboard-select" required>
                                    <option value="">Pilih Supplier</option>
                                    <?php $__currentLoopData = \App\Models\Supplier::active()->orderBy('name')->get(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supplier): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($supplier->id); ?>"><?php echo e($supplier->name); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div>
                                <label for="forward_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                    Catatan (Opsional)
                                </label>
                                <textarea id="forward_notes" name="admin_notes" rows="3"
                                          class="admin-dashboard-textarea"
                                          placeholder="Tambahkan catatan untuk supplier..."></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeForwardToSupplierModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Teruskan ke Supplier
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Complete Modal -->
<div id="completeModal" class="admin-dashboard-modal">
    <div class="admin-dashboard-modal-content">
        <div class="admin-dashboard-modal-header">
            <h3 class="admin-dashboard-modal-title">Selesaikan Retur</h3>
            <button type="button" class="admin-dashboard-modal-close" onclick="closeCompleteModal()">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>
        <form id="completeForm" method="POST">
            <?php echo csrf_field(); ?>
            <?php echo method_field('PUT'); ?>
            <div class="admin-dashboard-modal-body">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1">
                        <h4 class="text-lg font-medium text-gray-900 mb-2">Konfirmasi Penyelesaian</h4>
                        <p class="text-sm text-gray-600 mb-4">
                            Apakah Anda yakin ingin menyelesaikan retur ini? Stok toko akan dikurangi sesuai jumlah retur.
                        </p>
                        <div>
                            <label for="complete_notes" class="block text-sm font-medium text-gray-700 mb-2">
                                Catatan (Opsional)
                            </label>
                            <textarea id="complete_notes" name="admin_notes" rows="3"
                                      class="admin-dashboard-textarea"
                                      placeholder="Tambahkan catatan penyelesaian..."></textarea>
                        </div>
                    </div>
                </div>
            </div>
            <div class="admin-dashboard-modal-footer">
                <button type="button" onclick="closeCompleteModal()" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                    Batal
                </button>
                <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                    Ya, Selesaikan Retur
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
// Approve Modal Functions
function openApproveModal(returnId) {
    const modal = document.getElementById('approveModal');
    const form = document.getElementById('approveForm');

    form.action = `/admin/store-returns/${returnId}/approve`;
    modal.classList.add('active');
}

function closeApproveModal() {
    const modal = document.getElementById('approveModal');
    modal.classList.remove('active');
    document.getElementById('approveForm').reset();
}

// Reject Modal Functions
function openRejectModal(returnId) {
    const modal = document.getElementById('rejectModal');
    const form = document.getElementById('rejectForm');

    form.action = `/admin/store-returns/${returnId}/reject`;
    modal.classList.add('active');
}

function closeRejectModal() {
    const modal = document.getElementById('rejectModal');
    modal.classList.remove('active');
    document.getElementById('rejectForm').reset();
}

// Forward to Supplier Modal Functions
function openForwardToSupplierModal(returnId, productName) {
    const modal = document.getElementById('forwardToSupplierModal');
    const form = document.getElementById('forwardToSupplierForm');
    const productNameElement = document.getElementById('forwardProductName');

    form.action = `/admin/store-returns/${returnId}/forward-to-supplier`;
    productNameElement.textContent = productName;
    modal.classList.add('active');
}

function closeForwardToSupplierModal() {
    const modal = document.getElementById('forwardToSupplierModal');
    modal.classList.remove('active');
    document.getElementById('forwardToSupplierForm').reset();
}

// Complete Modal Functions
function openCompleteModal(returnId) {
    const modal = document.getElementById('completeModal');
    const form = document.getElementById('completeForm');

    form.action = `/admin/store-returns/${returnId}/complete`;
    modal.classList.add('active');
}

function closeCompleteModal() {
    const modal = document.getElementById('completeModal');
    modal.classList.remove('active');
    document.getElementById('completeForm').reset();
}

// Close modals when clicking outside
document.getElementById('approveModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeApproveModal();
    }
});

document.getElementById('rejectModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeRejectModal();
    }
});

document.getElementById('forwardToSupplierModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeForwardToSupplierModal();
    }
});

document.getElementById('completeModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeCompleteModal();
    }
});
</script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/admin/store-returns/index.blade.php ENDPATH**/ ?>