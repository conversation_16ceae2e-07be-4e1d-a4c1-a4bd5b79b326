<?php

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\TimePeriodFilter;
use App\Models\Distribution;
use App\Models\DistributionItem;
use App\Models\WarehouseStock;
use App\Models\StockMovement;
use Illuminate\Http\Request;

class UserDeliveryController extends Controller
{
    use TimePeriodFilter;
    /**
     * Display a listing of deliveries for the user's store
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $query = Distribution::where('store_id', $store->id)
            ->with(['product', 'store']);

        // Apply time period filter
        $this->applyTimePeriodFilterDate($query, $request, 'date_distributed');

        // Get date range for form display
        $dateRange = $this->getDateRangeFromPeriod($request);
        $dateFrom = $dateRange['start'] ? $dateRange['start']->format('Y-m-d') : null;
        $dateTo = $dateRange['end'] ? $dateRange['end']->format('Y-m-d') : null;

        // Manual date override (if provided, override time period)
        if ($request->filled('date_from') || $request->filled('date_to')) {
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            if ($dateFrom) {
                $query->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $query->whereDate('date_distributed', '<=', $dateTo);
            }
        }

        // Apply status filter if provided (using confirmed boolean)
        if ($request->has('status') && $request->status) {
            if ($request->status === 'confirmed') {
                $query->where('confirmed', true);
            } elseif ($request->status === 'pending') {
                $query->where('confirmed', false);
            }
        }

        $deliveries = $query->orderBy('date_distributed', 'desc')
            ->paginate(15);

        // Get statistics for the current filter period
        $statsQuery = Distribution::where('store_id', $store->id);

        // Apply same date filters to stats
        if ($request->filled('date_from') || $request->filled('date_to')) {
            if ($dateFrom) {
                $statsQuery->whereDate('date_distributed', '>=', $dateFrom);
            }
            if ($dateTo) {
                $statsQuery->whereDate('date_distributed', '<=', $dateTo);
            }
        } else {
            // Apply time period filter to stats
            $this->applyTimePeriodFilterDate($statsQuery, $request, 'date_distributed');
        }

        $stats = [
            'total' => $statsQuery->count(),
            'pending' => $statsQuery->where('confirmed', false)->count(),
            'confirmed' => $statsQuery->where('confirmed', true)->count(),
            'with_shortage' => $statsQuery->where('confirmed', true)
                ->whereColumn('received_quantity', '<', 'quantity')->count(),
        ];

        // Get filter options
        $statuses = [
            'pending' => 'Belum Dikonfirmasi',
            'confirmed' => 'Dikonfirmasi'
        ];

        return view('user.deliveries', compact('deliveries', 'statuses', 'dateFrom', 'dateTo', 'stats'));
    }

    /**
     * Display the specified delivery
     */
    public function show($id)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $delivery = Distribution::where('store_id', $store->id)
            ->with(['product', 'store'])
            ->findOrFail($id);

        return view('user.deliveries.show', compact('delivery'));
    }

    /**
     * Confirm delivery receipt
     */
    public function confirm(Request $request, $id)
    {
        $user = auth()->user();
        $store = $user->store;

        if (!$store) {
            return redirect()->route('user.dashboard')->with('error', 'Anda belum terdaftar di toko manapun.');
        }

        $delivery = Distribution::where('store_id', $store->id)
            ->where('confirmed', false)
            ->findOrFail($id);

        // Use database transaction to ensure data consistency
        \DB::transaction(function () use ($delivery, $store) {
            // Log for debugging
            \Log::info('UserDeliveryController: Starting distribution confirmation', [
                'distribution_id' => $delivery->id,
                'product_id' => $delivery->product_id,
                'quantity' => $delivery->quantity,
                'store_id' => $store->id
            ]);

            // Update distribution status
            $delivery->update([
                'confirmed' => true,
                'received_quantity' => $delivery->quantity, // Assume full quantity received in simple confirmation
                'confirmed_at' => now(),
            ]);

            // CRITICAL FIX: Reduce warehouse stock by the distributed quantity
            // This ensures warehouse stock reflects what was sent out
            $warehouseStock = WarehouseStock::where('product_id', $delivery->product_id)->first();
            if ($warehouseStock) {
                $previousWarehouseStock = $warehouseStock->quantity;
                $newWarehouseStock = $previousWarehouseStock - $delivery->quantity;

                \Log::info('UserDeliveryController: Updating warehouse stock', [
                    'product_id' => $delivery->product_id,
                    'previous_stock' => $previousWarehouseStock,
                    'reduction' => $delivery->quantity,
                    'new_stock' => $newWarehouseStock
                ]);

                $warehouseStock->update([
                    'quantity' => $newWarehouseStock
                ]);

                // Create stock movement record for warehouse stock reduction
                StockMovement::create([
                    'product_id' => $delivery->product_id,
                    'type' => 'out',
                    'source' => 'distribution',
                    'quantity' => $delivery->quantity,
                    'previous_stock' => $previousWarehouseStock,
                    'new_stock' => $newWarehouseStock,
                    'reference_type' => 'Distribution',
                    'reference_id' => $delivery->id,
                    'notes' => "Distribusi dikonfirmasi ke toko {$store->name}. Jumlah: {$delivery->quantity} unit",
                    'created_by' => auth()->id(),
                ]);

                \Log::info('UserDeliveryController: Warehouse stock updated successfully');
            } else {
                \Log::error('UserDeliveryController: No warehouse stock found for product', [
                    'product_id' => $delivery->product_id
                ]);
            }

            // Update or create store stock
            $existingStock = \App\Models\StoreStock::where('store_id', $store->id)
                ->where('product_id', $delivery->product_id)
                ->first();

            if ($existingStock) {
                $existingStock->update([
                    'quantity' => $existingStock->quantity + $delivery->quantity,
                ]);
            } else {
                \App\Models\StoreStock::create([
                    'store_id' => $store->id,
                    'product_id' => $delivery->product_id,
                    'quantity' => $delivery->quantity,
                ]);
            }
        });

        return redirect()->route('user.deliveries')
            ->with('success', 'Distribusi berhasil dikonfirmasi dan stok toko telah diperbarui.');
    }
}
