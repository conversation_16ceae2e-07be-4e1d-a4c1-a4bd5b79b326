@extends('layouts.user')

@section('title', 'Buat Retur - Dashboard Toko')
@section('page-title', 'Buat Retur')

@section('content')
<div class="user-dashboard-returns-container">
    <!-- Header -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <div class="user-dashboard-card-header-content">
                <h1 class="user-dashboard-card-title"><PERSON><PERSON><PERSON></h1>
                <p class="user-dashboard-card-subtitle">Laporkan produk rusak atau bermasalah untuk diretur</p>
            </div>
            <div class="user-dashboard-card-actions">
                <a href="{{ route('user.returns.index') }}" class="user-dashboard-btn user-dashboard-btn-secondary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                    </svg>
                    Kembali
                </a>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Informasi Retur</h2>
        </div>
        <div class="user-dashboard-card-content">
            <form method="POST" action="{{ route('user.returns.store') }}" class="space-y-6">
                @csrf

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Product -->
                    <div>
                        <label for="product_id" class="block text-sm font-medium text-gray-700 mb-2">
                            Produk <span class="text-red-500">*</span>
                        </label>
                        <select id="product_id" 
                                name="product_id" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('product_id') border-red-500 @enderror"
                                required>
                            <option value="">Pilih Produk</option>
                            @foreach($products as $product)
                                <option value="{{ $product->id }}" 
                                        data-stock="{{ $product->storeStock->where('store_id', auth()->user()->store_id)->first()->quantity ?? 0 }}"
                                        {{ old('product_id') == $product->id ? 'selected' : '' }}>
                                    {{ $product->name }} (Stok: {{ $product->storeStock->where('store_id', auth()->user()->store_id)->first()->quantity ?? 0 }})
                                </option>
                            @endforeach
                        </select>
                        @error('product_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Quantity -->
                    <div>
                        <label for="quantity" class="block text-sm font-medium text-gray-700 mb-2">
                            Jumlah <span class="text-red-500">*</span>
                        </label>
                        <input type="number" 
                               id="quantity" 
                               name="quantity" 
                               value="{{ old('quantity') }}"
                               min="1"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('quantity') border-red-500 @enderror"
                               placeholder="Masukkan jumlah"
                               required>
                        <p class="mt-1 text-sm text-gray-500" id="stock-info">Pilih produk untuk melihat stok tersedia</p>
                        @error('quantity')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Reason -->
                    <div>
                        <label for="reason" class="block text-sm font-medium text-gray-700 mb-2">
                            Alasan Retur <span class="text-red-500">*</span>
                        </label>
                        <select id="reason" 
                                name="reason" 
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('reason') border-red-500 @enderror"
                                required>
                            <option value="">Pilih Alasan</option>
                            <option value="damaged" {{ old('reason') === 'damaged' ? 'selected' : '' }}>Rusak</option>
                            <option value="expired" {{ old('reason') === 'expired' ? 'selected' : '' }}>Kadaluarsa</option>
                            <option value="defective" {{ old('reason') === 'defective' ? 'selected' : '' }}>Cacat</option>
                            <option value="overstock" {{ old('reason') === 'overstock' ? 'selected' : '' }}>Kelebihan Stok</option>
                            <option value="other" {{ old('reason') === 'other' ? 'selected' : '' }}>Lainnya</option>
                        </select>
                        @error('reason')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Return Date -->
                    <div>
                        <label for="return_date" class="block text-sm font-medium text-gray-700 mb-2">
                            Tanggal Retur <span class="text-red-500">*</span>
                        </label>
                        <input type="date" 
                               id="return_date" 
                               name="return_date" 
                               value="{{ old('return_date', date('Y-m-d')) }}"
                               max="{{ date('Y-m-d') }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('return_date') border-red-500 @enderror"
                               required>
                        @error('return_date')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>

                <!-- Supplier (Optional) -->
                <div>
                    <label for="supplier_id" class="block text-sm font-medium text-gray-700 mb-2">
                        Supplier Tujuan (Opsional)
                    </label>
                    <select id="supplier_id" 
                            name="supplier_id" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('supplier_id') border-red-500 @enderror">
                        <option value="">Pilih Supplier (Kosongkan jika retur ke gudang)</option>
                        @foreach($suppliers as $supplier)
                            <option value="{{ $supplier->id }}" {{ old('supplier_id') == $supplier->id ? 'selected' : '' }}>
                                {{ $supplier->name }}
                            </option>
                        @endforeach
                    </select>
                    <p class="mt-1 text-sm text-gray-500">Jika tidak dipilih, produk akan diretur ke gudang pusat</p>
                    @error('supplier_id')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Deskripsi Masalah <span class="text-red-500">*</span>
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('description') border-red-500 @enderror"
                              placeholder="Jelaskan secara detail kondisi produk dan alasan retur..."
                              required>{{ old('description') }}</textarea>
                    @error('description')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Important Notice -->
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">Penting!</h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <ul class="list-disc list-inside space-y-1">
                                    <li>Pastikan produk masih dalam kondisi yang dapat diretur</li>
                                    <li>Permintaan retur akan direview oleh admin sebelum disetujui</li>
                                    <li>Stok akan dikurangi setelah retur disetujui dan diproses</li>
                                    <li>Berikan deskripsi yang jelas untuk mempercepat proses persetujuan</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
                    <button type="submit" class="inline-flex items-center justify-center px-6 py-3 bg-blue-600 text-white text-sm font-medium rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Kirim Permintaan Retur
                    </button>
                    <a href="{{ route('user.returns.index') }}" class="inline-flex items-center justify-center px-6 py-3 bg-gray-600 text-white text-sm font-medium rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const productSelect = document.getElementById('product_id');
    const quantityInput = document.getElementById('quantity');
    const stockInfo = document.getElementById('stock-info');
    
    function updateStockInfo() {
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        if (selectedOption.value) {
            const stock = selectedOption.getAttribute('data-stock');
            stockInfo.textContent = `Stok tersedia: ${stock}`;
            quantityInput.max = stock;
            
            if (parseInt(stock) === 0) {
                stockInfo.textContent = 'Produk ini tidak memiliki stok';
                stockInfo.className = 'mt-1 text-sm text-red-600';
                quantityInput.disabled = true;
            } else {
                stockInfo.className = 'mt-1 text-sm text-gray-500';
                quantityInput.disabled = false;
            }
        } else {
            stockInfo.textContent = 'Pilih produk untuk melihat stok tersedia';
            stockInfo.className = 'mt-1 text-sm text-gray-500';
            quantityInput.max = '';
            quantityInput.disabled = false;
        }
    }
    
    productSelect.addEventListener('change', updateStockInfo);
    
    // Update on page load if there's a selected value
    updateStockInfo();
    
    // Validate quantity against stock
    quantityInput.addEventListener('input', function() {
        const selectedOption = productSelect.options[productSelect.selectedIndex];
        if (selectedOption.value) {
            const stock = parseInt(selectedOption.getAttribute('data-stock'));
            const quantity = parseInt(this.value);
            
            if (quantity > stock) {
                this.setCustomValidity(`Jumlah tidak boleh melebihi stok tersedia (${stock})`);
            } else {
                this.setCustomValidity('');
            }
        }
    });
});
</script>
@endpush
@endsection
