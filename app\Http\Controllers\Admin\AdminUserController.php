<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class AdminUserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index()
    {
        $users = User::with('store')->orderBy('created_at', 'desc')->paginate(15);

        // Set custom pagination view for admin dashboard
        $users->withPath(request()->url());

        return view('admin.users.index', compact('users'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create()
    {
        $storeLocations = $this->getStoreLocations();

        return view('admin.users.create', compact('storeLocations'));
    }

    /**
     * Store a newly created user in storage
     */
    public function store(Request $request)
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:6|confirmed',
            'role' => 'required|in:admin,user,supplier_admin',
        ];

        // Add store validation based on role and selection
        if ($request->role === 'user') {
            if ($request->input('store_location') === 'new') {
                $rules['new_store_location'] = 'required|string|max:255';
            } else {
                $rules['store_location'] = 'required|string|max:255';
            }
        }

        $validatedData = $request->validate($rules, [
            'name.required' => 'Nama wajib diisi',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah digunakan',
            'password.required' => 'Kata sandi wajib diisi',
            'password.min' => 'Kata sandi minimal 6 karakter',
            'password.confirmed' => 'Konfirmasi kata sandi tidak cocok',
            'role.required' => 'Peran wajib dipilih',
            'role.in' => 'Peran tidak valid',
            'store_location.required' => 'Lokasi toko wajib diisi untuk pengguna toko',
            'new_store_location.required' => 'Nama lokasi toko baru wajib diisi',
            'new_store_location.string' => 'Nama lokasi toko baru harus berupa teks',
        ]);

        // Handle store assignment
        $storeId = null;
        if ($request->role === 'user') {
            if ($request->input('store_location') === 'new' && $request->filled('new_store_location')) {
                // Create new store
                $locationName = $validatedData['new_store_location'];
                $storeName = str_starts_with($locationName, 'Toko ') ? $locationName : 'Toko ' . $locationName;
                $store = \App\Models\Store::create([
                    'name' => $storeName,
                    'location' => $locationName,
                ]);
                $storeId = $store->id;
            } elseif ($request->input('store_location') !== 'new') {
                // Find existing store by location
                $store = \App\Models\Store::where('location', $validatedData['store_location'])->first();
                if ($store) {
                    $storeId = $store->id;
                } else {
                    // Create store if it doesn't exist
                    $locationName = $validatedData['store_location'];
                    $storeName = str_starts_with($locationName, 'Toko ') ? $locationName : 'Toko ' . $locationName;
                    $store = \App\Models\Store::create([
                        'name' => $storeName,
                        'location' => $locationName,
                    ]);
                    $storeId = $store->id;
                }
            }
        }

        // Create the user
        $user = User::create([
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'password' => Hash::make($validatedData['password']),
            'role' => $validatedData['role'],
            'store_id' => $storeId,
        ]);

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dibuat: ' . $user->name);
    }

    /**
     * Display the specified user
     */
    public function show(User $user)
    {
        $user->load('store');
        return view('admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user)
    {
        $storeLocations = $this->getStoreLocations();
        
        return view('admin.users.edit', compact('user', 'storeLocations'));
    }

    /**
     * Update the specified user in storage
     */
    public function update(Request $request, User $user)
    {
        $rules = [
            'name' => 'required|string|max:255',
            'email' => ['required', 'email', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:admin,user,supplier_admin',
        ];

        // Add password validation only if provided
        if ($request->filled('password')) {
            $rules['password'] = 'string|min:6|confirmed';
        }

        // Add store_location validation based on role and selection
        if ($request->role === 'user') {
            if ($request->input('store_location') === 'new') {
                $rules['new_store_location'] = 'required|string|max:255';
            } else {
                $rules['store_location'] = 'required|string|max:255';
            }
        } else {
            $rules['store_location'] = 'nullable|string|max:255';
        }

        $validatedData = $request->validate($rules, [
            'name.required' => 'Nama wajib diisi',
            'email.required' => 'Email wajib diisi',
            'email.email' => 'Format email tidak valid',
            'email.unique' => 'Email sudah digunakan',
            'password.min' => 'Kata sandi minimal 6 karakter',
            'password.confirmed' => 'Konfirmasi kata sandi tidak cocok',
            'role.required' => 'Peran wajib dipilih',
            'role.in' => 'Peran tidak valid',
            'store_location.required' => 'Lokasi toko wajib diisi untuk user',
            'new_store_location.required' => 'Nama lokasi toko baru wajib diisi',
            'new_store_location.string' => 'Nama lokasi toko baru harus berupa teks',
        ]);

        // Handle store assignment
        $storeId = null;
        if ($request->role === 'user') {
            if ($request->input('store_location') === 'new' && $request->filled('new_store_location')) {
                // Create new store
                $locationName = $validatedData['new_store_location'];
                $storeName = str_starts_with($locationName, 'Toko ') ? $locationName : 'Toko ' . $locationName;
                $store = \App\Models\Store::create([
                    'name' => $storeName,
                    'location' => $locationName,
                ]);
                $storeId = $store->id;
            } elseif ($request->input('store_location') !== 'new') {
                // Find existing store by location
                $store = \App\Models\Store::where('location', $validatedData['store_location'])->first();
                if ($store) {
                    $storeId = $store->id;
                } else {
                    // Create store if it doesn't exist
                    $locationName = $validatedData['store_location'];
                    $storeName = str_starts_with($locationName, 'Toko ') ? $locationName : 'Toko ' . $locationName;
                    $store = \App\Models\Store::create([
                        'name' => $storeName,
                        'location' => $locationName,
                    ]);
                    $storeId = $store->id;
                }
            }
        }

        // Update user data
        $updateData = [
            'name' => $validatedData['name'],
            'email' => $validatedData['email'],
            'role' => $validatedData['role'],
            'store_id' => $storeId,
        ];

        // Only update password if provided
        if ($request->filled('password')) {
            $updateData['password'] = Hash::make($validatedData['password']);
        }

        $user->update($updateData);

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil diperbarui: ' . $user->name);
    }

    /**
     * Remove the specified user from storage
     */
    public function destroy(User $user)
    {
        // Prevent deleting the current user
        if ($user->id === auth()->id()) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Anda tidak dapat menghapus akun Anda sendiri');
        }

        $userName = $user->name;
        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'Pengguna berhasil dihapus: ' . $userName);
    }

    /**
     * Get available store locations
     */
    private function getStoreLocations()
    {
        // Get all unique store locations from the stores table
        return \App\Models\Store::orderBy('location')->pluck('location')->unique()->values();
    }
}
