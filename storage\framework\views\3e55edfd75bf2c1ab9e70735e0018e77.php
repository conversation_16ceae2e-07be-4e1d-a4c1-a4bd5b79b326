<?php $__env->startSection('title', 'Distribusi - Indah Berkah Abadi'); ?>

<?php $__env->startPush('styles'); ?>
<!-- Independent CSS for User Dashboard Dropdowns -->
<link rel="stylesheet" href="<?php echo e(asset('css/user-dashboard-dropdowns.css')); ?>">
<!-- Independent CSS for User Dashboard Deliveries -->
<link rel="stylesheet" href="<?php echo e(asset('css/user-dashboard-deliveries.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Distribusi</h1>
            <p class="user-dashboard-header-subtitle">Pantau distribusi dari gudang ke toko Anda</p>
        </div>
    </div>

    <!-- Time Period Filter -->
    <?php echo $__env->make('user.components.time-period-filter', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['total'])); ?></div>
                        <div class="text-xs text-gray-600">Total Distribusi</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-yellow-50 rounded-lg">
                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['pending'])); ?></div>
                        <div class="text-xs text-gray-600">Menunggu Konfirmasi</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['confirmed'])); ?></div>
                        <div class="text-xs text-gray-600">Dikonfirmasi</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="user-dashboard-card">
            <div class="user-dashboard-card-content">
                <div class="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center flex-shrink-0">
                        <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m5 14v-5a2 2 0 00-2-2H6a2 2 0 00-2 2v5a2 2 0 002 2h14a2 2 0 002-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <div class="text-lg font-bold text-gray-900"><?php echo e(number_format($stats['with_shortage'])); ?></div>
                        <div class="text-xs text-gray-600">Dengan Kekurangan</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="user-dashboard-search-container">
        <div class="user-dashboard-card-content">
            <form method="GET" class="user-dashboard-search-form">
                <!-- Preserve time period parameter -->
                <?php if(request('period')): ?>
                    <input type="hidden" name="period" value="<?php echo e(request('period')); ?>">
                <?php endif; ?>
                <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                    <label for="status-filter" class="user-dashboard-form-label">Status</label>
                    <select id="status-filter" name="status" class="user-dashboard-form-select user-dashboard-form-select-enhanced">
                        <option value="">Semua Status</option>
                        <?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($value); ?>" <?php echo e(request('status') == $value ? 'selected' : ''); ?>>
                                <?php echo e($label); ?>

                            </option>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </select>
                </div>
                <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                    <label for="date-from" class="user-dashboard-form-label">Dari Tanggal</label>
                    <input id="date-from" type="date" name="date_from" value="<?php echo e($dateFrom ?? request('date_from')); ?>" class="user-dashboard-form-input">
                </div>
                <div class="user-dashboard-filter-group user-dashboard-filter-group-enhanced">
                    <label for="date-to" class="user-dashboard-form-label">Sampai Tanggal</label>
                    <input id="date-to" type="date" name="date_to" value="<?php echo e($dateTo ?? request('date_to')); ?>" class="user-dashboard-form-input">
                </div>
                <div class="flex items-end">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg px-4 py-2 text-sm flex items-center w-full user-dashboard-btn-sm">Filter</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Distributions Table -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Daftar Distribusi</h2>
            <p class="user-dashboard-card-description"><?php echo e($deliveries->total()); ?> distribusi ditemukan</p>
        </div>
        <div class="user-dashboard-card-content">
            <?php if($deliveries->count() > 0): ?>
                <div class="user-dashboard-deliveries-table-container">
                    <table class="user-dashboard-deliveries-table">
                        <thead class="user-dashboard-deliveries-table-header">
                            <tr>
                                <th class="user-dashboard-deliveries-table-th">Produk</th>
                                <th class="user-dashboard-deliveries-table-th">Jumlah</th>
                                <th class="user-dashboard-deliveries-table-th">Tanggal Distribusi</th>
                                <th class="user-dashboard-deliveries-table-th">Status</th>
                                <th class="user-dashboard-deliveries-table-th">Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $deliveries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $delivery): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="user-dashboard-deliveries-table-row">
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-product-info">
                                        <div class="user-dashboard-deliveries-product-name"><?php echo e($delivery->product ? $delivery->product->name : 'Produk tidak diketahui'); ?></div>
                                        <?php if($delivery->confirmed && $delivery->notes): ?>
                                        <div class="user-dashboard-deliveries-product-notes"><?php echo e(Str::limit($delivery->notes, 50)); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-quantity-info">
                                        <div class="user-dashboard-deliveries-quantity-main"><?php echo e(number_format($delivery->quantity)); ?> unit</div>
                                        <?php if($delivery->confirmed && $delivery->received_quantity !== null): ?>
                                        <div class="user-dashboard-deliveries-quantity-received">
                                            Diterima: <?php echo e(number_format($delivery->received_quantity)); ?> unit
                                            <?php if($delivery->received_quantity != $delivery->quantity): ?>
                                            <span class="user-dashboard-deliveries-quantity-difference">
                                                <?php if($delivery->received_quantity < $delivery->quantity): ?>
                                                    (kurang <?php echo e(number_format($delivery->quantity - $delivery->received_quantity)); ?>)
                                                <?php else: ?>
                                                    (lebih <?php echo e(number_format($delivery->received_quantity - $delivery->quantity)); ?>)
                                                <?php endif; ?>
                                            </span>
                                            <?php endif; ?>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-date-info">
                                        <div class="user-dashboard-deliveries-date-main"><?php echo e($delivery->date_distributed ? $delivery->date_distributed->format('d M Y') : 'Belum dijadwalkan'); ?></div>
                                        <?php if($delivery->confirmed && $delivery->confirmed_at): ?>
                                        <div class="user-dashboard-deliveries-date-confirmed">Dikonfirmasi: <?php echo e($delivery->confirmed_at->format('d M Y')); ?></div>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <span class="user-dashboard-deliveries-status-badge user-dashboard-deliveries-status-<?php echo e($delivery->confirmed ? 'confirmed' : 'pending'); ?>">
                                        <?php echo e($delivery->confirmed ? 'Dikonfirmasi' : 'Menunggu Konfirmasi'); ?>

                                    </span>
                                </td>
                                <td class="user-dashboard-deliveries-table-td">
                                    <div class="user-dashboard-deliveries-actions">
                                        <a href="<?php echo e(route('user.deliveries.show', $delivery->id)); ?>"
                                           class="user-dashboard-deliveries-action-btn user-dashboard-deliveries-action-view">
                                            Lihat Detail
                                        </a>
                                        <?php if(!$delivery->confirmed): ?>
                                        <form method="POST" action="<?php echo e(route('user.deliveries.confirm', $delivery->id)); ?>" style="display: inline;">
                                            <?php echo csrf_field(); ?>
                                            <?php echo method_field('PATCH'); ?>
                                            <button type="submit"
                                                    class="user-dashboard-deliveries-action-btn user-dashboard-deliveries-action-confirm"
                                                    onclick="return confirm('Konfirmasi penerimaan distribusi ini?')">
                                                Konfirmasi
                                            </button>
                                        </form>
                                        <?php elseif($delivery->confirmed && $delivery->received_quantity < $delivery->quantity): ?>
                                        <button onclick="openReturnModal('<?php echo e($delivery->id); ?>', '<?php echo e($delivery->product->name); ?>', '<?php echo e($delivery->quantity - $delivery->received_quantity); ?>', '<?php echo e($delivery->product_id); ?>')"
                                                class="user-dashboard-deliveries-action-btn user-dashboard-deliveries-action-return">
                                            Retur
                                        </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="user-dashboard-deliveries-pagination">
                    <?php echo e($deliveries->withQueryString()->links()); ?>

                </div>
            <?php else: ?>
                <div class="user-dashboard-deliveries-empty">
                    <svg class="user-dashboard-deliveries-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                    </svg>
                    <h3 class="user-dashboard-deliveries-empty-title">Tidak ada distribusi</h3>
                    <p class="user-dashboard-deliveries-empty-description">Belum ada distribusi yang ditemukan dengan filter yang dipilih.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Return Modal -->
<div id="returnModal" class="user-dashboard-deliveries-modal">
    <div class="user-dashboard-deliveries-modal-content">
        <div class="user-dashboard-deliveries-modal-header">
            <h3 class="user-dashboard-deliveries-modal-title">Buat Permintaan Retur</h3>
            <button type="button" onclick="closeReturnModal()" class="user-dashboard-deliveries-modal-close">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <form id="returnForm" method="POST" action="<?php echo e(route('user.returns.store-from-distribution')); ?>">
            <?php echo csrf_field(); ?>
            <input type="hidden" id="distribution_id" name="distribution_id" value="">
            <input type="hidden" id="product_id" name="product_id" value="">

            <div class="user-dashboard-deliveries-modal-body">
                <div class="user-dashboard-deliveries-form-group">
                    <div class="user-dashboard-deliveries-info-box">
                        <div class="user-dashboard-deliveries-info-content">
                            <svg class="user-dashboard-deliveries-info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <p class="user-dashboard-deliveries-info-text">Buat retur untuk produk <strong id="returnProductName"></strong> yang tidak diterima sesuai jumlah yang dikirim.</p>
                        </div>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_quantity" class="user-dashboard-deliveries-form-label">
                            Jumlah Retur (Maksimal: <span id="maxReturnQuantity"></span>)
                        </label>
                        <input type="number"
                               id="return_quantity"
                               name="quantity"
                               class="user-dashboard-deliveries-form-input"
                               min="1"
                               value="1"
                               required>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_reason" class="user-dashboard-deliveries-form-label">
                            Alasan Retur <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <select id="return_reason" name="reason" class="user-dashboard-deliveries-form-select" required>
                            <option value="">Pilih Alasan</option>
                            <option value="damaged">Rusak</option>
                            <option value="expired">Kadaluarsa</option>
                            <option value="defective">Cacat</option>
                            <option value="overstock">Kelebihan Stok</option>
                            <option value="shortage">Kekurangan Pengiriman</option>
                            <option value="other">Lainnya</option>
                        </select>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_description" class="user-dashboard-deliveries-form-label">
                            Deskripsi Detail <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <textarea id="return_description"
                                  name="description"
                                  rows="3"
                                  class="user-dashboard-deliveries-form-textarea"
                                  placeholder="Jelaskan detail masalah atau alasan retur..."
                                  required></textarea>
                    </div>

                    <div class="user-dashboard-deliveries-form-field">
                        <label for="return_date" class="user-dashboard-deliveries-form-label">
                            Tanggal Retur <span class="user-dashboard-deliveries-form-required">*</span>
                        </label>
                        <input type="date"
                               id="return_date"
                               name="return_date"
                               class="user-dashboard-deliveries-form-input"
                               max="<?php echo e(date('Y-m-d')); ?>"
                               value="<?php echo e(date('Y-m-d')); ?>"
                               required>
                    </div>
                </div>
            </div>

            <div class="user-dashboard-deliveries-modal-footer">
                <button type="button" onclick="closeReturnModal()" class="user-dashboard-deliveries-btn user-dashboard-deliveries-btn-outline">
                    Batal
                </button>
                <button type="submit" class="user-dashboard-deliveries-btn user-dashboard-deliveries-btn-primary">
                    Buat Retur
                </button>
            </div>
        </form>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<!-- Independent JavaScript for User Dashboard Dropdowns -->
<script src="<?php echo e(asset('js/user-dashboard-dropdowns.js')); ?>"></script>
<!-- Independent JavaScript for User Dashboard Deliveries -->
<script src="<?php echo e(asset('js/user-dashboard-deliveries.js')); ?>"></script>
<?php $__env->stopPush(); ?>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/user/deliveries.blade.php ENDPATH**/ ?>