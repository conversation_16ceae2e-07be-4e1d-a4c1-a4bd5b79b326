<?php

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Contracts\Console\Kernel;

$app = require_once 'bootstrap/app.php';
$app->make(Kernel::class)->bootstrap();

// Check if supplier admin users exist
$supplierAdmins = App\Models\User::where('role', 'supplier_admin')->get();

echo "Supplier Admin Users:\n";
foreach ($supplierAdmins as $admin) {
    echo "- {$admin->name} ({$admin->email})\n";
}

// Check if suppliers exist
$suppliers = App\Models\Supplier::all();
echo "\nSuppliers:\n";
foreach ($suppliers as $supplier) {
    echo "- {$supplier->name} ({$supplier->status})\n";
}

// Check if returns exist
$returns = App\Models\ReturnModel::with(['product', 'supplier'])->get();
echo "\nReturns:\n";
foreach ($returns as $return) {
    echo "- Product: {$return->product->name}, Supplier: {$return->supplier->name}, Status: {$return->status}\n";
}

// Check if cancelled deliveries exist
$cancelledDeliveries = App\Models\SupplierDelivery::where('status', 'cancelled')->with(['product', 'supplier'])->get();
echo "\nCancelled Deliveries:\n";
foreach ($cancelledDeliveries as $delivery) {
    echo "- Product: {$delivery->product->name}, Supplier: {$delivery->supplier->name}, Quantity: {$delivery->quantity}\n";
}

echo "\nTest completed!\n";
