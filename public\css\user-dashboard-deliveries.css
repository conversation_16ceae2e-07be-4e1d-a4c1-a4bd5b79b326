/* User Dashboard Deliveries Table Styles */

/* Table Container */
.user-dashboard-deliveries-table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
}

/* Table Base */
.user-dashboard-deliveries-table {
    width: 100%;
    text-align: left;
    font-size: 0.875rem;
    line-height: 1.25rem;
    border-collapse: collapse;
}

/* Table Header */
.user-dashboard-deliveries-table-header {
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.user-dashboard-deliveries-table-th {
    padding: 12px 24px;
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
}

/* Table Body */
.user-dashboard-deliveries-table-row {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e7eb;
    transition: background-color 0.15s ease-in-out;
}

.user-dashboard-deliveries-table-row:hover {
    background-color: #f9fafb;
}

.user-dashboard-deliveries-table-td {
    padding: 16px 24px;
    vertical-align: top;
}

/* Product Information */
.user-dashboard-deliveries-product-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-dashboard-deliveries-product-name {
    font-weight: 500;
    color: #111827;
    line-height: 1.25;
}

.user-dashboard-deliveries-product-notes {
    font-size: 0.75rem;
    color: #6b7280;
    line-height: 1.25;
}

/* Quantity Information */
.user-dashboard-deliveries-quantity-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-dashboard-deliveries-quantity-main {
    font-weight: 500;
    color: #111827;
}

.user-dashboard-deliveries-quantity-received {
    font-size: 0.75rem;
    color: #6b7280;
}

.user-dashboard-deliveries-quantity-difference {
    font-size: 0.75rem;
    color: #dc2626;
    font-weight: 500;
}

/* Date Information */
.user-dashboard-deliveries-date-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.user-dashboard-deliveries-date-main {
    font-weight: 500;
    color: #111827;
}

.user-dashboard-deliveries-date-confirmed {
    font-size: 0.75rem;
    color: #059669;
}

/* Status Badges */
.user-dashboard-deliveries-status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    text-transform: capitalize;
}

.user-dashboard-deliveries-status-confirmed {
    background-color: #d1fae5;
    color: #065f46;
}

.user-dashboard-deliveries-status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

/* Action Buttons */
.user-dashboard-deliveries-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.user-dashboard-deliveries-action-btn {
    display: inline-flex;
    align-items: center;
    padding: 6px 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 6px;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    white-space: nowrap;
}

.user-dashboard-deliveries-action-view {
    background-color: #dbeafe;
    color: #1e40af;
}

.user-dashboard-deliveries-action-view:hover {
    background-color: #bfdbfe;
    color: #1d4ed8;
}

.user-dashboard-deliveries-action-confirm {
    background-color: #d1fae5;
    color: #065f46;
}

.user-dashboard-deliveries-action-confirm:hover {
    background-color: #a7f3d0;
    color: #047857;
}

.user-dashboard-deliveries-action-return {
    background-color: #fed7aa;
    color: #c2410c;
}

.user-dashboard-deliveries-action-return:hover {
    background-color: #fdba74;
    color: #ea580c;
}

/* Pagination */
.user-dashboard-deliveries-pagination {
    margin-top: 24px;
    padding-top: 16px;
    border-top: 1px solid #e5e7eb;
}

/* Empty State */
.user-dashboard-deliveries-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 48px 24px;
    text-align: center;
}

.user-dashboard-deliveries-empty-icon {
    width: 48px;
    height: 48px;
    color: #d1d5db;
    margin-bottom: 16px;
}

.user-dashboard-deliveries-empty-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #111827;
    margin-bottom: 8px;
}

.user-dashboard-deliveries-empty-description {
    font-size: 0.875rem;
    color: #6b7280;
}

/* Modal Styles */
.user-dashboard-deliveries-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease-in-out;
}

.user-dashboard-deliveries-modal.active {
    opacity: 1;
    visibility: visible;
}

.user-dashboard-deliveries-modal-content {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    transform: scale(0.95);
    transition: transform 0.3s ease-in-out;
}

.user-dashboard-deliveries-modal.active .user-dashboard-deliveries-modal-content {
    transform: scale(1);
}

.user-dashboard-deliveries-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 24px 0 24px;
    border-bottom: 1px solid #e5e7eb;
    margin-bottom: 24px;
}

.user-dashboard-deliveries-modal-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #111827;
}

.user-dashboard-deliveries-modal-close {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 6px;
    transition: all 0.15s ease-in-out;
}

.user-dashboard-deliveries-modal-close:hover {
    background-color: #f3f4f6;
    color: #374151;
}

.user-dashboard-deliveries-modal-body {
    padding: 0 24px;
}

.user-dashboard-deliveries-modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    padding: 24px;
    border-top: 1px solid #e5e7eb;
    margin-top: 24px;
}

/* Form Styles */
.user-dashboard-deliveries-form-group {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.user-dashboard-deliveries-form-field {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.user-dashboard-deliveries-form-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
}

.user-dashboard-deliveries-form-required {
    color: #dc2626;
}

.user-dashboard-deliveries-form-input,
.user-dashboard-deliveries-form-select,
.user-dashboard-deliveries-form-textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    font-size: 0.875rem;
    transition: all 0.15s ease-in-out;
}

.user-dashboard-deliveries-form-input:focus,
.user-dashboard-deliveries-form-select:focus,
.user-dashboard-deliveries-form-textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-dashboard-deliveries-form-textarea {
    resize: vertical;
    min-height: 80px;
}

.user-dashboard-deliveries-info-box {
    background-color: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 8px;
    padding: 16px;
}

.user-dashboard-deliveries-info-content {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.user-dashboard-deliveries-info-icon {
    width: 20px;
    height: 20px;
    color: #2563eb;
    flex-shrink: 0;
    margin-top: 2px;
}

.user-dashboard-deliveries-info-text {
    font-size: 0.875rem;
    color: #1e40af;
    line-height: 1.5;
}

/* Button Styles */
.user-dashboard-deliveries-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    text-decoration: none;
    transition: all 0.15s ease-in-out;
    min-width: 80px;
}

.user-dashboard-deliveries-btn-primary {
    background-color: #3b82f6;
    color: #ffffff;
}

.user-dashboard-deliveries-btn-primary:hover {
    background-color: #2563eb;
}

.user-dashboard-deliveries-btn-outline {
    background-color: transparent;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

.user-dashboard-deliveries-btn-outline:hover {
    background-color: #f9fafb;
    color: #374151;
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-dashboard-deliveries-table-th,
    .user-dashboard-deliveries-table-td {
        padding: 12px 16px;
    }
    
    .user-dashboard-deliveries-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .user-dashboard-deliveries-action-btn {
        justify-content: center;
        width: 100%;
    }
    
    .user-dashboard-deliveries-modal-content {
        width: 95%;
        margin: 20px;
    }
    
    .user-dashboard-deliveries-modal-header,
    .user-dashboard-deliveries-modal-body,
    .user-dashboard-deliveries-modal-footer {
        padding-left: 16px;
        padding-right: 16px;
    }
}
