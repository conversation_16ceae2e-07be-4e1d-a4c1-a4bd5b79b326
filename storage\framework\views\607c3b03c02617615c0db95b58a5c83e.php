

<?php $__env->startSection('title', 'Stok Toko - Indah Berkah Abadi'); ?>

<?php $__env->startPush('styles'); ?>
<!-- Independent CSS for User Inventory Search -->
<link rel="stylesheet" href="<?php echo e(asset('css/user-inventory-search.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="user-dashboard-container">
    <!-- Header -->
    <div class="user-dashboard-header">
        <div class="user-dashboard-header-content">
            <h1 class="user-dashboard-header-title">Stok Toko</h1>
            <p class="user-dashboard-header-subtitle">Pantau stok produk yang tersedia di toko Anda</p>
        </div>
        <div class="user-dashboard-header-actions">
            <a href="<?php echo e(route('user.deliveries')); ?>" class="user-dashboard-btn-primary user-dashboard-btn-sm">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
                Lihat Distribusi
            </a>
        </div>
    </div>

    <!-- Search - Independent Implementation -->
    <div class="user-inventory-search-wrapper">
        <form method="GET" action="<?php echo e(route('user.inventory')); ?>" class="user-inventory-search-form" id="userInventorySearchForm">
            <div class="user-inventory-input-group" style="order:2; max-width:180px; flex:0 0 180px;">
                <label class="user-inventory-label" style="visibility: hidden;">Action</label>
                <button type="submit"
                        id="userInventorySearchButton"
                        class="user-inventory-button">
                    <svg class="user-inventory-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                    </svg>
                    Cari Produk
                </button>
            </div>
            <div class="user-inventory-input-group" style="order:1; flex:1 1 0; min-width:0;">
                <label for="userInventorySearchInput" class="user-inventory-label">Cari Produk</label>
                <input type="text"
                       id="userInventorySearchInput"
                       name="search"
                       value="<?php echo e(request('search')); ?>"
                       placeholder="Masukkan nama produk..."
                       class="user-inventory-input"
                       autocomplete="off">
            </div>
        </form>
    </div>

    <!-- Store Stock List -->
    <div class="user-dashboard-card">
        <div class="user-dashboard-card-header">
            <h2 class="user-dashboard-card-title">Stok Toko</h2>
            <p class="user-dashboard-card-description"><?php echo e($storeStock->total()); ?> produk ditemukan</p>
        </div>
        <div class="user-dashboard-card-content">
            <?php if($storeInventory->count() > 0): ?>
                <div class="user-dashboard-table-container">
                    <table class="user-dashboard-table">
                        <thead class="user-dashboard-table-header">
                            <tr>
                                <th class="user-dashboard-table-header-cell">Produk</th>
                                <th class="user-dashboard-table-header-cell">Stok Saat Ini</th>
                                <th class="user-dashboard-table-header-cell">Status</th>
                                <th class="user-dashboard-table-header-cell">Terakhir Diterima</th>
                            </tr>
                        </thead>
                        <tbody class="user-dashboard-table-body">
                            <?php $__currentLoopData = $storeInventory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr class="user-dashboard-table-row">
                                <td class="user-dashboard-table-cell">
                                    <p class="font-medium text-gray-900"><?php echo e($item['name']); ?></p>
                                </td>
                                <td class="user-dashboard-table-cell">
                                    <div class="flex items-center space-x-2">
                                        <span class="font-medium"><?php echo e(number_format($item['current_stock'])); ?></span>
                                        <span class="text-sm text-gray-500">unit</span>
                                    </div>
                                </td>
                                <td class="user-dashboard-table-cell">
                                    <span class="user-dashboard-badge-<?php echo e($item['status'] === 'good' ? 'green' : ($item['status'] === 'low' ? 'yellow' : 'red')); ?>">
                                        <?php switch($item['status']):
                                            case ('good'): ?>
                                                Stok Baik
                                                <?php break; ?>
                                            <?php case ('low'): ?>
                                                Stok Rendah
                                                <?php break; ?>
                                            <?php case ('out'): ?>
                                                Stok Habis
                                                <?php break; ?>
                                            <?php default: ?>
                                                <?php echo e(ucfirst($item['status'])); ?>

                                        <?php endswitch; ?>
                                    </span>
                                </td>
                                <td class="user-dashboard-table-cell">
                                    <span class="text-sm text-gray-600"><?php echo e($item['last_received']); ?></span>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    <?php echo e($storeStock->withQueryString()->links()); ?>

                </div>
            <?php else: ?>
                <div class="user-dashboard-empty-state">
                    <svg class="user-dashboard-empty-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                    </svg>
                    <h3 class="user-dashboard-empty-title">Tidak ada produk</h3>
                    <p class="user-dashboard-empty-description">Belum ada produk yang ditemukan dengan filter yang dipilih.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>

    <!-- Quick Stats -->
    <div class="user-dashboard-stat-grid">
        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Total Produk</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e($storeInventory->count()); ?></div>
            <div class="user-dashboard-stat-description">Jenis produk</div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Stok Baik</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e($storeInventory->where('status', 'good')->count()); ?></div>
            <div class="user-dashboard-stat-description">Produk</div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Stok Rendah</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e($storeInventory->whereIn('status', ['low', 'critical'])->count()); ?></div>
            <div class="user-dashboard-stat-description">Produk</div>
        </div>

        <div class="user-dashboard-stat-card">
            <div class="user-dashboard-stat-header">
                <h3 class="user-dashboard-stat-title">Stok Habis</h3>
                <svg class="user-dashboard-stat-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </div>
            <div class="user-dashboard-stat-value"><?php echo e($storeInventory->where('status', 'out')->count()); ?></div>
            <div class="user-dashboard-stat-description">Produk</div>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<!-- Independent JavaScript for User Inventory Search -->
<script src="<?php echo e(asset('js/user-inventory-search.js')); ?>"></script>
<!-- Debug script to identify blocking issues -->
<script src="<?php echo e(asset('js/debug-search-blocking.js')); ?>"></script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.user', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\00. RENATA\CODE\indahberkahabadi\resources\views/user/inventory.blade.php ENDPATH**/ ?>