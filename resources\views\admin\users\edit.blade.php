@extends('layouts.admin')

@section('title', 'Edit Pengguna - Indah Berkah Abadi')
@section('page-title', 'Edit Pengguna')

@section('content')
<div class="space-y-6">
    <!-- Header -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-content">
            <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div>
                    <h1 class="text-2xl sm:text-3xl font-bold text-gray-900">Edit Pengguna</h1>
                    <p class="text-gray-600 mt-1">Perbarui informasi pengguna {{ $user->name }}</p>
                </div>
                <div class="flex flex-col sm:flex-row gap-3">
                    <a href="{{ route('admin.users.show', $user) }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        Kembali
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Form -->
    <div class="admin-dashboard-card">
        <div class="admin-dashboard-card-header">
            <h2 class="admin-dashboard-card-title">Informasi Pengguna</h2>
        </div>
        <div class="admin-dashboard-card-content">
            <form action="{{ route('admin.users.update', $user) }}" method="POST" class="space-y-6">
                @csrf
                @method('PUT')

                <!-- Name Field -->
                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                        Nama Lengkap <span class="text-red-500">*</span>
                    </label>
                    <input type="text" 
                           id="name" 
                           name="name" 
                           value="{{ old('name', $user->name) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('name') border-red-500 @enderror"
                           placeholder="Masukkan nama lengkap"
                           required>
                    @error('name')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Email Field -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        Email <span class="text-red-500">*</span>
                    </label>
                    <input type="email" 
                           id="email" 
                           name="email" 
                           value="{{ old('email', $user->email) }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('email') border-red-500 @enderror"
                           placeholder="<EMAIL>"
                           required>
                    @error('email')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Role Field -->
                <div>
                    <label for="role" class="block text-sm font-medium text-gray-700 mb-2">
                        Peran <span class="text-red-500">*</span>
                    </label>
                    <select id="role" 
                            name="role" 
                            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('role') border-red-500 @enderror"
                            required
                            onchange="toggleStoreLocation()">
                        <option value="">Pilih Peran</option>
                        <option value="admin" {{ old('role', $user->role) == 'admin' ? 'selected' : '' }}>Administrator Gudang</option>
                        <option value="user" {{ old('role', $user->role) == 'user' ? 'selected' : '' }}>Pengguna Toko</option>
                        <option value="supplier_admin" {{ old('role', $user->role) == 'supplier_admin' ? 'selected' : '' }}>Admin Supplier</option>
                    </select>
                    @error('role')
                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                    @enderror
                </div>

                <!-- Store Location Field (conditional) -->
                <div id="store-location-field" style="display: {{ old('role', $user->role) == 'user' ? 'block' : 'none' }};">
                    <div class="space-y-4">
                        <div>
                            <label for="store_location" class="block text-sm font-medium text-gray-700 mb-2">
                                Lokasi Toko <span class="text-red-500">*</span>
                            </label>
                            <select id="store_location"
                                    name="store_location"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('store_location') border-red-500 @enderror"
                                    onchange="toggleNewStoreLocationField()">
                                <option value="">Pilih Lokasi Toko</option>
                                @foreach($storeLocations as $location)
                                <option value="{{ $location }}" {{ old('store_location', $user->store_location) == $location ? 'selected' : '' }}>{{ $location }}</option>
                                @endforeach
                                <option value="new">+ Tambah Lokasi Toko Baru</option>
                            </select>
                            @error('store_location')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- New Store Location Field (hidden by default) -->
                        <div id="new-store-location-field" style="display: none;">
                            <label for="new_store_location" class="block text-sm font-medium text-gray-700 mb-2">
                                Nama Lokasi Toko Baru <span class="text-red-500">*</span>
                            </label>
                            <input
                                type="text"
                                id="new_store_location"
                                name="new_store_location"
                                value="{{ old('new_store_location') }}"
                                class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('new_store_location') border-red-500 @enderror"
                                placeholder="Masukkan nama lokasi toko baru (contoh: Toko Bali)"
                            >
                            @error('new_store_location')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    <p class="mt-1 text-sm text-gray-500">Wajib diisi untuk pengguna toko</p>
                </div>

                <!-- Password Section -->
                <div class="border-t border-gray-200 pt-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Ubah Kata Sandi</h3>
                    <p class="text-sm text-gray-600 mb-4">Kosongkan jika tidak ingin mengubah kata sandi</p>
                    
                    <!-- Password Field -->
                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            Kata Sandi Baru
                        </label>
                        <input type="password" 
                               id="password" 
                               name="password" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 @error('password') border-red-500 @enderror"
                               placeholder="Minimal 6 karakter">
                        @error('password')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>

                    <!-- Password Confirmation Field -->
                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-2">
                            Konfirmasi Kata Sandi Baru
                        </label>
                        <input type="password" 
                               id="password_confirmation" 
                               name="password_confirmation" 
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                               placeholder="Ulangi kata sandi baru">
                    </div>
                </div>

                <!-- Submit Buttons -->
                <div class="flex flex-col sm:flex-row gap-3 pt-6 border-t border-gray-200">
                    <button type="submit" class="admin-dashboard-btn admin-dashboard-btn-primary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        Simpan Perubahan
                    </button>
                    <a href="{{ route('admin.users.show', $user) }}" class="admin-dashboard-btn admin-dashboard-btn-secondary">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                        Batal
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleStoreLocation() {
    const roleSelect = document.getElementById('role');
    const storeLocationField = document.getElementById('store-location-field');
    const storeLocationSelect = document.getElementById('store_location');

    if (roleSelect.value === 'user') {
        storeLocationField.style.display = 'block';
        storeLocationSelect.required = true;
    } else {
        storeLocationField.style.display = 'none';
        storeLocationSelect.required = false;
        storeLocationSelect.value = '';
        // Also hide new store location field
        const newStoreLocationField = document.getElementById('new-store-location-field');
        const newStoreLocationInput = document.getElementById('new_store_location');
        if (newStoreLocationField) {
            newStoreLocationField.style.display = 'none';
            newStoreLocationInput.required = false;
            newStoreLocationInput.value = '';
        }
    }
}

function toggleNewStoreLocationField() {
    const storeLocationSelect = document.getElementById('store_location');
    const newStoreLocationField = document.getElementById('new-store-location-field');
    const newStoreLocationInput = document.getElementById('new_store_location');

    if (storeLocationSelect.value === 'new') {
        newStoreLocationField.style.display = 'block';
        newStoreLocationInput.required = true;
    } else {
        newStoreLocationField.style.display = 'none';
        newStoreLocationInput.required = false;
        newStoreLocationInput.value = '';
    }
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    toggleStoreLocation();

    // Add form submission handler
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            const roleSelect = document.getElementById('role');
            const storeLocationSelect = document.getElementById('store_location');
            const newStoreLocationInput = document.getElementById('new_store_location');

            // Only validate if role is user
            if (roleSelect.value === 'user') {
                // If "new" is selected but no name provided, prevent submission
                if (storeLocationSelect.value === 'new' && !newStoreLocationInput.value.trim()) {
                    e.preventDefault();
                    alert('Silakan masukkan nama lokasi toko baru atau pilih lokasi yang sudah ada.');
                    newStoreLocationInput.focus();
                    return false;
                }

                // If existing location is selected, clear new location name
                if (storeLocationSelect.value !== 'new') {
                    newStoreLocationInput.value = '';
                }
            }
        });
    }
});
</script>
@endsection
